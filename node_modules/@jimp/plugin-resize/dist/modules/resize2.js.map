{"version": 3, "file": "resize2.js", "names": ["operations", "nearestNeighbor", "src", "dst", "wSrc", "width", "hSrc", "height", "wDst", "hDst", "bufSrc", "data", "buf<PERSON>t", "i", "j", "posDst", "iSrc", "Math", "floor", "jSrc", "posSrc", "bilinearInterpolation", "interpolate", "k", "kMin", "vMin", "kMax", "vMax", "round", "assign", "pos", "offset", "x", "xMin", "xMax", "y", "yMin", "yMax", "posMin", "posMax", "min", "ceil", "_interpolate2D", "options", "wM", "max", "wDst2", "hM", "hDst2", "buf1", "<PERSON><PERSON><PERSON>", "alloc", "xPos", "t", "srcPos", "buf1Pos", "kPos", "x0", "x1", "x2", "x3", "buf2", "yPos", "buf2Pos", "y0", "y1", "y2", "y3", "m", "r", "g", "b", "a", "realColors", "xyPos", "pixelAlpha", "bicubicInterpolation", "interpolateCubic", "a0", "a1", "a2", "a3", "hermiteInterpolation", "interpolateHermite", "c0", "c1", "c2", "c3", "bezierInterpolation", "interpolate<PERSON><PERSON><PERSON>", "cp1", "cp2", "nt"], "sources": ["../../src/modules/resize2.js"], "sourcesContent": ["/**\n * Copyright (c) 2015 <PERSON><PERSON> Roche\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:</p>\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nconst operations = {\n  nearestNeighbor(src, dst) {\n    const wSrc = src.width;\n    const hSrc = src.height;\n\n    const wDst = dst.width;\n    const hDst = dst.height;\n\n    const bufSrc = src.data;\n    const bufDst = dst.data;\n\n    for (let i = 0; i < hDst; i++) {\n      for (let j = 0; j < wDst; j++) {\n        let posDst = (i * wDst + j) * 4;\n\n        const iSrc = Math.floor((i * hSrc) / hDst);\n        const jSrc = Math.floor((j * wSrc) / wDst);\n        let posSrc = (iSrc * wSrc + jSrc) * 4;\n\n        bufDst[posDst++] = bufSrc[posSrc++];\n        bufDst[posDst++] = bufSrc[posSrc++];\n        bufDst[posDst++] = bufSrc[posSrc++];\n        bufDst[posDst++] = bufSrc[posSrc++];\n      }\n    }\n  },\n\n  bilinearInterpolation(src, dst) {\n    const wSrc = src.width;\n    const hSrc = src.height;\n\n    const wDst = dst.width;\n    const hDst = dst.height;\n\n    const bufSrc = src.data;\n    const bufDst = dst.data;\n\n    const interpolate = function (k, kMin, vMin, kMax, vMax) {\n      // special case - k is integer\n      if (kMin === kMax) {\n        return vMin;\n      }\n\n      return Math.round((k - kMin) * vMax + (kMax - k) * vMin);\n    };\n\n    const assign = function (pos, offset, x, xMin, xMax, y, yMin, yMax) {\n      let posMin = (yMin * wSrc + xMin) * 4 + offset;\n      let posMax = (yMin * wSrc + xMax) * 4 + offset;\n      const vMin = interpolate(x, xMin, bufSrc[posMin], xMax, bufSrc[posMax]);\n\n      // special case, y is integer\n      if (yMax === yMin) {\n        bufDst[pos + offset] = vMin;\n      } else {\n        posMin = (yMax * wSrc + xMin) * 4 + offset;\n        posMax = (yMax * wSrc + xMax) * 4 + offset;\n        const vMax = interpolate(x, xMin, bufSrc[posMin], xMax, bufSrc[posMax]);\n\n        bufDst[pos + offset] = interpolate(y, yMin, vMin, yMax, vMax);\n      }\n    };\n\n    for (let i = 0; i < hDst; i++) {\n      for (let j = 0; j < wDst; j++) {\n        const posDst = (i * wDst + j) * 4;\n        // x & y in src coordinates\n        const x = (j * wSrc) / wDst;\n        const xMin = Math.floor(x);\n        const xMax = Math.min(Math.ceil(x), wSrc - 1);\n\n        const y = (i * hSrc) / hDst;\n        const yMin = Math.floor(y);\n        const yMax = Math.min(Math.ceil(y), hSrc - 1);\n\n        assign(posDst, 0, x, xMin, xMax, y, yMin, yMax);\n        assign(posDst, 1, x, xMin, xMax, y, yMin, yMax);\n        assign(posDst, 2, x, xMin, xMax, y, yMin, yMax);\n        assign(posDst, 3, x, xMin, xMax, y, yMin, yMax);\n      }\n    }\n  },\n\n  _interpolate2D(src, dst, options, interpolate) {\n    const bufSrc = src.data;\n    const bufDst = dst.data;\n\n    const wSrc = src.width;\n    const hSrc = src.height;\n\n    const wDst = dst.width;\n    const hDst = dst.height;\n\n    // when dst smaller than src/2, interpolate first to a multiple between 0.5 and 1.0 src, then sum squares\n    const wM = Math.max(1, Math.floor(wSrc / wDst));\n    const wDst2 = wDst * wM;\n    const hM = Math.max(1, Math.floor(hSrc / hDst));\n    const hDst2 = hDst * hM;\n\n    // ===========================================================\n    // Pass 1 - interpolate rows\n    // buf1 has width of dst2 and height of src\n    const buf1 = Buffer.alloc(wDst2 * hSrc * 4);\n    for (let i = 0; i < hSrc; i++) {\n      for (let j = 0; j < wDst2; j++) {\n        // i in src coords, j in dst coords\n\n        // calculate x in src coords\n        // this interpolation requires 4 sample points and the two inner ones must be real\n        // the outer points can be fudged for the edges.\n        // therefore (wSrc-1)/wDst2\n        const x = (j * (wSrc - 1)) / wDst2;\n        const xPos = Math.floor(x);\n        const t = x - xPos;\n        const srcPos = (i * wSrc + xPos) * 4;\n        const buf1Pos = (i * wDst2 + j) * 4;\n\n        for (let k = 0; k < 4; k++) {\n          const kPos = srcPos + k;\n          const x0 =\n            xPos > 0 ? bufSrc[kPos - 4] : 2 * bufSrc[kPos] - bufSrc[kPos + 4];\n          const x1 = bufSrc[kPos];\n          const x2 = bufSrc[kPos + 4];\n          const x3 =\n            xPos < wSrc - 2\n              ? bufSrc[kPos + 8]\n              : 2 * bufSrc[kPos + 4] - bufSrc[kPos];\n          buf1[buf1Pos + k] = interpolate(x0, x1, x2, x3, t);\n        }\n      }\n    }\n    // this._writeFile(wDst2, hSrc, buf1, \"out/buf1.jpg\");\n\n    // ===========================================================\n    // Pass 2 - interpolate columns\n    // buf2 has width and height of dst2\n    const buf2 = Buffer.alloc(wDst2 * hDst2 * 4);\n    for (let i = 0; i < hDst2; i++) {\n      for (let j = 0; j < wDst2; j++) {\n        // i&j in dst2 coords\n\n        // calculate y in buf1 coords\n        // this interpolation requires 4 sample points and the two inner ones must be real\n        // the outer points can be fudged for the edges.\n        // therefore (hSrc-1)/hDst2\n        const y = (i * (hSrc - 1)) / hDst2;\n        const yPos = Math.floor(y);\n        const t = y - yPos;\n        const buf1Pos = (yPos * wDst2 + j) * 4;\n        const buf2Pos = (i * wDst2 + j) * 4;\n        for (let k = 0; k < 4; k++) {\n          const kPos = buf1Pos + k;\n          const y0 =\n            yPos > 0\n              ? buf1[kPos - wDst2 * 4]\n              : 2 * buf1[kPos] - buf1[kPos + wDst2 * 4];\n          const y1 = buf1[kPos];\n          const y2 = buf1[kPos + wDst2 * 4];\n          const y3 =\n            yPos < hSrc - 2\n              ? buf1[kPos + wDst2 * 8]\n              : 2 * buf1[kPos + wDst2 * 4] - buf1[kPos];\n\n          buf2[buf2Pos + k] = interpolate(y0, y1, y2, y3, t);\n        }\n      }\n    }\n    // this._writeFile(wDst2, hDst2, buf2, \"out/buf2.jpg\");\n\n    // ===========================================================\n    // Pass 3 - scale to dst\n    const m = wM * hM;\n    if (m > 1) {\n      for (let i = 0; i < hDst; i++) {\n        for (let j = 0; j < wDst; j++) {\n          // i&j in dst bounded coords\n          let r = 0;\n          let g = 0;\n          let b = 0;\n          let a = 0;\n          let realColors = 0;\n\n          for (let y = 0; y < hM; y++) {\n            const yPos = i * hM + y;\n\n            for (let x = 0; x < wM; x++) {\n              const xPos = j * wM + x;\n              const xyPos = (yPos * wDst2 + xPos) * 4;\n              const pixelAlpha = buf2[xyPos + 3];\n\n              if (pixelAlpha) {\n                r += buf2[xyPos];\n                g += buf2[xyPos + 1];\n                b += buf2[xyPos + 2];\n                realColors++;\n              }\n\n              a += pixelAlpha;\n            }\n          }\n\n          const pos = (i * wDst + j) * 4;\n          bufDst[pos] = realColors ? Math.round(r / realColors) : 0;\n          bufDst[pos + 1] = realColors ? Math.round(g / realColors) : 0;\n          bufDst[pos + 2] = realColors ? Math.round(b / realColors) : 0;\n          bufDst[pos + 3] = Math.round(a / m);\n        }\n      }\n    } else {\n      // replace dst buffer with buf2\n      dst.data = buf2;\n    }\n  },\n\n  bicubicInterpolation(src, dst, options) {\n    const interpolateCubic = function (x0, x1, x2, x3, t) {\n      const a0 = x3 - x2 - x0 + x1;\n      const a1 = x0 - x1 - a0;\n      const a2 = x2 - x0;\n      const a3 = x1;\n      return Math.max(\n        0,\n        Math.min(255, a0 * (t * t * t) + a1 * (t * t) + a2 * t + a3)\n      );\n    };\n\n    return this._interpolate2D(src, dst, options, interpolateCubic);\n  },\n\n  hermiteInterpolation(src, dst, options) {\n    const interpolateHermite = function (x0, x1, x2, x3, t) {\n      const c0 = x1;\n      const c1 = 0.5 * (x2 - x0);\n      const c2 = x0 - 2.5 * x1 + 2 * x2 - 0.5 * x3;\n      const c3 = 0.5 * (x3 - x0) + 1.5 * (x1 - x2);\n      return Math.max(\n        0,\n        Math.min(255, Math.round(((c3 * t + c2) * t + c1) * t + c0))\n      );\n    };\n\n    return this._interpolate2D(src, dst, options, interpolateHermite);\n  },\n\n  bezierInterpolation(src, dst, options) {\n    // between 2 points y(n), y(n+1), use next points out, y(n-1), y(n+2)\n    // to predict control points (a & b) to be placed at n+0.5\n    //  ya(n) = y(n) + (y(n+1)-y(n-1))/4\n    //  yb(n) = y(n+1) - (y(n+2)-y(n))/4\n    // then use std bezier to interpolate [n,n+1)\n    //  y(n+t) = y(n)*(1-t)^3 + 3 * ya(n)*(1-t)^2*t + 3 * yb(n)*(1-t)*t^2 + y(n+1)*t^3\n    //  note the 3* factor for the two control points\n    // for edge cases, can choose:\n    //  y(-1) = y(0) - 2*(y(1)-y(0))\n    //  y(w) = y(w-1) + 2*(y(w-1)-y(w-2))\n    // but can go with y(-1) = y(0) and y(w) = y(w-1)\n    const interpolateBezier = function (x0, x1, x2, x3, t) {\n      // x1, x2 are the knots, use x0 and x3 to calculate control points\n      const cp1 = x1 + (x2 - x0) / 4;\n      const cp2 = x2 - (x3 - x1) / 4;\n      const nt = 1 - t;\n      const c0 = x1 * nt * nt * nt;\n      const c1 = 3 * cp1 * nt * nt * t;\n      const c2 = 3 * cp2 * nt * t * t;\n      const c3 = x2 * t * t * t;\n      return Math.max(0, Math.min(255, Math.round(c0 + c1 + c2 + c3)));\n    };\n\n    return this._interpolate2D(src, dst, options, interpolateBezier);\n  },\n};\n\nexport default operations;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,UAAU,GAAG;EACjBC,eAAe,CAACC,GAAG,EAAEC,GAAG,EAAE;IACxB,MAAMC,IAAI,GAAGF,GAAG,CAACG,KAAK;IACtB,MAAMC,IAAI,GAAGJ,GAAG,CAACK,MAAM;IAEvB,MAAMC,IAAI,GAAGL,GAAG,CAACE,KAAK;IACtB,MAAMI,IAAI,GAAGN,GAAG,CAACI,MAAM;IAEvB,MAAMG,MAAM,GAAGR,GAAG,CAACS,IAAI;IACvB,MAAMC,MAAM,GAAGT,GAAG,CAACQ,IAAI;IAEvB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,EAAEI,CAAC,EAAE,EAAE;MAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,EAAEM,CAAC,EAAE,EAAE;QAC7B,IAAIC,MAAM,GAAG,CAACF,CAAC,GAAGL,IAAI,GAAGM,CAAC,IAAI,CAAC;QAE/B,MAAME,IAAI,GAAGC,IAAI,CAACC,KAAK,CAAEL,CAAC,GAAGP,IAAI,GAAIG,IAAI,CAAC;QAC1C,MAAMU,IAAI,GAAGF,IAAI,CAACC,KAAK,CAAEJ,CAAC,GAAGV,IAAI,GAAII,IAAI,CAAC;QAC1C,IAAIY,MAAM,GAAG,CAACJ,IAAI,GAAGZ,IAAI,GAAGe,IAAI,IAAI,CAAC;QAErCP,MAAM,CAACG,MAAM,EAAE,CAAC,GAAGL,MAAM,CAACU,MAAM,EAAE,CAAC;QACnCR,MAAM,CAACG,MAAM,EAAE,CAAC,GAAGL,MAAM,CAACU,MAAM,EAAE,CAAC;QACnCR,MAAM,CAACG,MAAM,EAAE,CAAC,GAAGL,MAAM,CAACU,MAAM,EAAE,CAAC;QACnCR,MAAM,CAACG,MAAM,EAAE,CAAC,GAAGL,MAAM,CAACU,MAAM,EAAE,CAAC;MACrC;IACF;EACF,CAAC;EAEDC,qBAAqB,CAACnB,GAAG,EAAEC,GAAG,EAAE;IAC9B,MAAMC,IAAI,GAAGF,GAAG,CAACG,KAAK;IACtB,MAAMC,IAAI,GAAGJ,GAAG,CAACK,MAAM;IAEvB,MAAMC,IAAI,GAAGL,GAAG,CAACE,KAAK;IACtB,MAAMI,IAAI,GAAGN,GAAG,CAACI,MAAM;IAEvB,MAAMG,MAAM,GAAGR,GAAG,CAACS,IAAI;IACvB,MAAMC,MAAM,GAAGT,GAAG,CAACQ,IAAI;IAEvB,MAAMW,WAAW,GAAG,UAAUC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;MACvD;MACA,IAAIH,IAAI,KAAKE,IAAI,EAAE;QACjB,OAAOD,IAAI;MACb;MAEA,OAAOR,IAAI,CAACW,KAAK,CAAC,CAACL,CAAC,GAAGC,IAAI,IAAIG,IAAI,GAAG,CAACD,IAAI,GAAGH,CAAC,IAAIE,IAAI,CAAC;IAC1D,CAAC;IAED,MAAMI,MAAM,GAAG,UAAUC,GAAG,EAAEC,MAAM,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAE;MAClE,IAAIC,MAAM,GAAG,CAACF,IAAI,GAAGhC,IAAI,GAAG6B,IAAI,IAAI,CAAC,GAAGF,MAAM;MAC9C,IAAIQ,MAAM,GAAG,CAACH,IAAI,GAAGhC,IAAI,GAAG8B,IAAI,IAAI,CAAC,GAAGH,MAAM;MAC9C,MAAMN,IAAI,GAAGH,WAAW,CAACU,CAAC,EAAEC,IAAI,EAAEvB,MAAM,CAAC4B,MAAM,CAAC,EAAEJ,IAAI,EAAExB,MAAM,CAAC6B,MAAM,CAAC,CAAC;;MAEvE;MACA,IAAIF,IAAI,KAAKD,IAAI,EAAE;QACjBxB,MAAM,CAACkB,GAAG,GAAGC,MAAM,CAAC,GAAGN,IAAI;MAC7B,CAAC,MAAM;QACLa,MAAM,GAAG,CAACD,IAAI,GAAGjC,IAAI,GAAG6B,IAAI,IAAI,CAAC,GAAGF,MAAM;QAC1CQ,MAAM,GAAG,CAACF,IAAI,GAAGjC,IAAI,GAAG8B,IAAI,IAAI,CAAC,GAAGH,MAAM;QAC1C,MAAMJ,IAAI,GAAGL,WAAW,CAACU,CAAC,EAAEC,IAAI,EAAEvB,MAAM,CAAC4B,MAAM,CAAC,EAAEJ,IAAI,EAAExB,MAAM,CAAC6B,MAAM,CAAC,CAAC;QAEvE3B,MAAM,CAACkB,GAAG,GAAGC,MAAM,CAAC,GAAGT,WAAW,CAACa,CAAC,EAAEC,IAAI,EAAEX,IAAI,EAAEY,IAAI,EAAEV,IAAI,CAAC;MAC/D;IACF,CAAC;IAED,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,EAAEI,CAAC,EAAE,EAAE;MAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,EAAEM,CAAC,EAAE,EAAE;QAC7B,MAAMC,MAAM,GAAG,CAACF,CAAC,GAAGL,IAAI,GAAGM,CAAC,IAAI,CAAC;QACjC;QACA,MAAMkB,CAAC,GAAIlB,CAAC,GAAGV,IAAI,GAAII,IAAI;QAC3B,MAAMyB,IAAI,GAAGhB,IAAI,CAACC,KAAK,CAACc,CAAC,CAAC;QAC1B,MAAME,IAAI,GAAGjB,IAAI,CAACuB,GAAG,CAACvB,IAAI,CAACwB,IAAI,CAACT,CAAC,CAAC,EAAE5B,IAAI,GAAG,CAAC,CAAC;QAE7C,MAAM+B,CAAC,GAAItB,CAAC,GAAGP,IAAI,GAAIG,IAAI;QAC3B,MAAM2B,IAAI,GAAGnB,IAAI,CAACC,KAAK,CAACiB,CAAC,CAAC;QAC1B,MAAME,IAAI,GAAGpB,IAAI,CAACuB,GAAG,CAACvB,IAAI,CAACwB,IAAI,CAACN,CAAC,CAAC,EAAE7B,IAAI,GAAG,CAAC,CAAC;QAE7CuB,MAAM,CAACd,MAAM,EAAE,CAAC,EAAEiB,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,CAAC;QAC/CR,MAAM,CAACd,MAAM,EAAE,CAAC,EAAEiB,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,CAAC;QAC/CR,MAAM,CAACd,MAAM,EAAE,CAAC,EAAEiB,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,CAAC;QAC/CR,MAAM,CAACd,MAAM,EAAE,CAAC,EAAEiB,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,CAAC;MACjD;IACF;EACF,CAAC;EAEDK,cAAc,CAACxC,GAAG,EAAEC,GAAG,EAAEwC,OAAO,EAAErB,WAAW,EAAE;IAC7C,MAAMZ,MAAM,GAAGR,GAAG,CAACS,IAAI;IACvB,MAAMC,MAAM,GAAGT,GAAG,CAACQ,IAAI;IAEvB,MAAMP,IAAI,GAAGF,GAAG,CAACG,KAAK;IACtB,MAAMC,IAAI,GAAGJ,GAAG,CAACK,MAAM;IAEvB,MAAMC,IAAI,GAAGL,GAAG,CAACE,KAAK;IACtB,MAAMI,IAAI,GAAGN,GAAG,CAACI,MAAM;;IAEvB;IACA,MAAMqC,EAAE,GAAG3B,IAAI,CAAC4B,GAAG,CAAC,CAAC,EAAE5B,IAAI,CAACC,KAAK,CAACd,IAAI,GAAGI,IAAI,CAAC,CAAC;IAC/C,MAAMsC,KAAK,GAAGtC,IAAI,GAAGoC,EAAE;IACvB,MAAMG,EAAE,GAAG9B,IAAI,CAAC4B,GAAG,CAAC,CAAC,EAAE5B,IAAI,CAACC,KAAK,CAACZ,IAAI,GAAGG,IAAI,CAAC,CAAC;IAC/C,MAAMuC,KAAK,GAAGvC,IAAI,GAAGsC,EAAE;;IAEvB;IACA;IACA;IACA,MAAME,IAAI,GAAGC,MAAM,CAACC,KAAK,CAACL,KAAK,GAAGxC,IAAI,GAAG,CAAC,CAAC;IAC3C,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,EAAEO,CAAC,EAAE,EAAE;MAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,EAAE,EAAE;QAC9B;;QAEA;QACA;QACA;QACA;QACA,MAAMkB,CAAC,GAAIlB,CAAC,IAAIV,IAAI,GAAG,CAAC,CAAC,GAAI0C,KAAK;QAClC,MAAMM,IAAI,GAAGnC,IAAI,CAACC,KAAK,CAACc,CAAC,CAAC;QAC1B,MAAMqB,CAAC,GAAGrB,CAAC,GAAGoB,IAAI;QAClB,MAAME,MAAM,GAAG,CAACzC,CAAC,GAAGT,IAAI,GAAGgD,IAAI,IAAI,CAAC;QACpC,MAAMG,OAAO,GAAG,CAAC1C,CAAC,GAAGiC,KAAK,GAAGhC,CAAC,IAAI,CAAC;QAEnC,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC1B,MAAMiC,IAAI,GAAGF,MAAM,GAAG/B,CAAC;UACvB,MAAMkC,EAAE,GACNL,IAAI,GAAG,CAAC,GAAG1C,MAAM,CAAC8C,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG9C,MAAM,CAAC8C,IAAI,CAAC,GAAG9C,MAAM,CAAC8C,IAAI,GAAG,CAAC,CAAC;UACnE,MAAME,EAAE,GAAGhD,MAAM,CAAC8C,IAAI,CAAC;UACvB,MAAMG,EAAE,GAAGjD,MAAM,CAAC8C,IAAI,GAAG,CAAC,CAAC;UAC3B,MAAMI,EAAE,GACNR,IAAI,GAAGhD,IAAI,GAAG,CAAC,GACXM,MAAM,CAAC8C,IAAI,GAAG,CAAC,CAAC,GAChB,CAAC,GAAG9C,MAAM,CAAC8C,IAAI,GAAG,CAAC,CAAC,GAAG9C,MAAM,CAAC8C,IAAI,CAAC;UACzCP,IAAI,CAACM,OAAO,GAAGhC,CAAC,CAAC,GAAGD,WAAW,CAACmC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEP,CAAC,CAAC;QACpD;MACF;IACF;IACA;;IAEA;IACA;IACA;IACA,MAAMQ,IAAI,GAAGX,MAAM,CAACC,KAAK,CAACL,KAAK,GAAGE,KAAK,GAAG,CAAC,CAAC;IAC5C,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,KAAK,EAAEnC,CAAC,EAAE,EAAE;MAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,EAAE,EAAE;QAC9B;;QAEA;QACA;QACA;QACA;QACA,MAAMqB,CAAC,GAAItB,CAAC,IAAIP,IAAI,GAAG,CAAC,CAAC,GAAI0C,KAAK;QAClC,MAAMc,IAAI,GAAG7C,IAAI,CAACC,KAAK,CAACiB,CAAC,CAAC;QAC1B,MAAMkB,CAAC,GAAGlB,CAAC,GAAG2B,IAAI;QAClB,MAAMP,OAAO,GAAG,CAACO,IAAI,GAAGhB,KAAK,GAAGhC,CAAC,IAAI,CAAC;QACtC,MAAMiD,OAAO,GAAG,CAAClD,CAAC,GAAGiC,KAAK,GAAGhC,CAAC,IAAI,CAAC;QACnC,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC1B,MAAMiC,IAAI,GAAGD,OAAO,GAAGhC,CAAC;UACxB,MAAMyC,EAAE,GACNF,IAAI,GAAG,CAAC,GACJb,IAAI,CAACO,IAAI,GAAGV,KAAK,GAAG,CAAC,CAAC,GACtB,CAAC,GAAGG,IAAI,CAACO,IAAI,CAAC,GAAGP,IAAI,CAACO,IAAI,GAAGV,KAAK,GAAG,CAAC,CAAC;UAC7C,MAAMmB,EAAE,GAAGhB,IAAI,CAACO,IAAI,CAAC;UACrB,MAAMU,EAAE,GAAGjB,IAAI,CAACO,IAAI,GAAGV,KAAK,GAAG,CAAC,CAAC;UACjC,MAAMqB,EAAE,GACNL,IAAI,GAAGxD,IAAI,GAAG,CAAC,GACX2C,IAAI,CAACO,IAAI,GAAGV,KAAK,GAAG,CAAC,CAAC,GACtB,CAAC,GAAGG,IAAI,CAACO,IAAI,GAAGV,KAAK,GAAG,CAAC,CAAC,GAAGG,IAAI,CAACO,IAAI,CAAC;UAE7CK,IAAI,CAACE,OAAO,GAAGxC,CAAC,CAAC,GAAGD,WAAW,CAAC0C,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEd,CAAC,CAAC;QACpD;MACF;IACF;IACA;;IAEA;IACA;IACA,MAAMe,CAAC,GAAGxB,EAAE,GAAGG,EAAE;IACjB,IAAIqB,CAAC,GAAG,CAAC,EAAE;MACT,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,EAAEI,CAAC,EAAE,EAAE;QAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,EAAEM,CAAC,EAAE,EAAE;UAC7B;UACA,IAAIuD,CAAC,GAAG,CAAC;UACT,IAAIC,CAAC,GAAG,CAAC;UACT,IAAIC,CAAC,GAAG,CAAC;UACT,IAAIC,CAAC,GAAG,CAAC;UACT,IAAIC,UAAU,GAAG,CAAC;UAElB,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,EAAE,EAAEZ,CAAC,EAAE,EAAE;YAC3B,MAAM2B,IAAI,GAAGjD,CAAC,GAAGkC,EAAE,GAAGZ,CAAC;YAEvB,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,EAAE,EAAEZ,CAAC,EAAE,EAAE;cAC3B,MAAMoB,IAAI,GAAGtC,CAAC,GAAG8B,EAAE,GAAGZ,CAAC;cACvB,MAAM0C,KAAK,GAAG,CAACZ,IAAI,GAAGhB,KAAK,GAAGM,IAAI,IAAI,CAAC;cACvC,MAAMuB,UAAU,GAAGd,IAAI,CAACa,KAAK,GAAG,CAAC,CAAC;cAElC,IAAIC,UAAU,EAAE;gBACdN,CAAC,IAAIR,IAAI,CAACa,KAAK,CAAC;gBAChBJ,CAAC,IAAIT,IAAI,CAACa,KAAK,GAAG,CAAC,CAAC;gBACpBH,CAAC,IAAIV,IAAI,CAACa,KAAK,GAAG,CAAC,CAAC;gBACpBD,UAAU,EAAE;cACd;cAEAD,CAAC,IAAIG,UAAU;YACjB;UACF;UAEA,MAAM7C,GAAG,GAAG,CAACjB,CAAC,GAAGL,IAAI,GAAGM,CAAC,IAAI,CAAC;UAC9BF,MAAM,CAACkB,GAAG,CAAC,GAAG2C,UAAU,GAAGxD,IAAI,CAACW,KAAK,CAACyC,CAAC,GAAGI,UAAU,CAAC,GAAG,CAAC;UACzD7D,MAAM,CAACkB,GAAG,GAAG,CAAC,CAAC,GAAG2C,UAAU,GAAGxD,IAAI,CAACW,KAAK,CAAC0C,CAAC,GAAGG,UAAU,CAAC,GAAG,CAAC;UAC7D7D,MAAM,CAACkB,GAAG,GAAG,CAAC,CAAC,GAAG2C,UAAU,GAAGxD,IAAI,CAACW,KAAK,CAAC2C,CAAC,GAAGE,UAAU,CAAC,GAAG,CAAC;UAC7D7D,MAAM,CAACkB,GAAG,GAAG,CAAC,CAAC,GAAGb,IAAI,CAACW,KAAK,CAAC4C,CAAC,GAAGJ,CAAC,CAAC;QACrC;MACF;IACF,CAAC,MAAM;MACL;MACAjE,GAAG,CAACQ,IAAI,GAAGkD,IAAI;IACjB;EACF,CAAC;EAEDe,oBAAoB,CAAC1E,GAAG,EAAEC,GAAG,EAAEwC,OAAO,EAAE;IACtC,MAAMkC,gBAAgB,GAAG,UAAUpB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEP,CAAC,EAAE;MACpD,MAAMyB,EAAE,GAAGlB,EAAE,GAAGD,EAAE,GAAGF,EAAE,GAAGC,EAAE;MAC5B,MAAMqB,EAAE,GAAGtB,EAAE,GAAGC,EAAE,GAAGoB,EAAE;MACvB,MAAME,EAAE,GAAGrB,EAAE,GAAGF,EAAE;MAClB,MAAMwB,EAAE,GAAGvB,EAAE;MACb,OAAOzC,IAAI,CAAC4B,GAAG,CACb,CAAC,EACD5B,IAAI,CAACuB,GAAG,CAAC,GAAG,EAAEsC,EAAE,IAAIzB,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAAC,GAAG0B,EAAE,IAAI1B,CAAC,GAAGA,CAAC,CAAC,GAAG2B,EAAE,GAAG3B,CAAC,GAAG4B,EAAE,CAAC,CAC7D;IACH,CAAC;IAED,OAAO,IAAI,CAACvC,cAAc,CAACxC,GAAG,EAAEC,GAAG,EAAEwC,OAAO,EAAEkC,gBAAgB,CAAC;EACjE,CAAC;EAEDK,oBAAoB,CAAChF,GAAG,EAAEC,GAAG,EAAEwC,OAAO,EAAE;IACtC,MAAMwC,kBAAkB,GAAG,UAAU1B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEP,CAAC,EAAE;MACtD,MAAM+B,EAAE,GAAG1B,EAAE;MACb,MAAM2B,EAAE,GAAG,GAAG,IAAI1B,EAAE,GAAGF,EAAE,CAAC;MAC1B,MAAM6B,EAAE,GAAG7B,EAAE,GAAG,GAAG,GAAGC,EAAE,GAAG,CAAC,GAAGC,EAAE,GAAG,GAAG,GAAGC,EAAE;MAC5C,MAAM2B,EAAE,GAAG,GAAG,IAAI3B,EAAE,GAAGH,EAAE,CAAC,GAAG,GAAG,IAAIC,EAAE,GAAGC,EAAE,CAAC;MAC5C,OAAO1C,IAAI,CAAC4B,GAAG,CACb,CAAC,EACD5B,IAAI,CAACuB,GAAG,CAAC,GAAG,EAAEvB,IAAI,CAACW,KAAK,CAAC,CAAC,CAAC2D,EAAE,GAAGlC,CAAC,GAAGiC,EAAE,IAAIjC,CAAC,GAAGgC,EAAE,IAAIhC,CAAC,GAAG+B,EAAE,CAAC,CAAC,CAC7D;IACH,CAAC;IAED,OAAO,IAAI,CAAC1C,cAAc,CAACxC,GAAG,EAAEC,GAAG,EAAEwC,OAAO,EAAEwC,kBAAkB,CAAC;EACnE,CAAC;EAEDK,mBAAmB,CAACtF,GAAG,EAAEC,GAAG,EAAEwC,OAAO,EAAE;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM8C,iBAAiB,GAAG,UAAUhC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEP,CAAC,EAAE;MACrD;MACA,MAAMqC,GAAG,GAAGhC,EAAE,GAAG,CAACC,EAAE,GAAGF,EAAE,IAAI,CAAC;MAC9B,MAAMkC,GAAG,GAAGhC,EAAE,GAAG,CAACC,EAAE,GAAGF,EAAE,IAAI,CAAC;MAC9B,MAAMkC,EAAE,GAAG,CAAC,GAAGvC,CAAC;MAChB,MAAM+B,EAAE,GAAG1B,EAAE,GAAGkC,EAAE,GAAGA,EAAE,GAAGA,EAAE;MAC5B,MAAMP,EAAE,GAAG,CAAC,GAAGK,GAAG,GAAGE,EAAE,GAAGA,EAAE,GAAGvC,CAAC;MAChC,MAAMiC,EAAE,GAAG,CAAC,GAAGK,GAAG,GAAGC,EAAE,GAAGvC,CAAC,GAAGA,CAAC;MAC/B,MAAMkC,EAAE,GAAG5B,EAAE,GAAGN,CAAC,GAAGA,CAAC,GAAGA,CAAC;MACzB,OAAOpC,IAAI,CAAC4B,GAAG,CAAC,CAAC,EAAE5B,IAAI,CAACuB,GAAG,CAAC,GAAG,EAAEvB,IAAI,CAACW,KAAK,CAACwD,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,OAAO,IAAI,CAAC7C,cAAc,CAACxC,GAAG,EAAEC,GAAG,EAAEwC,OAAO,EAAE8C,iBAAiB,CAAC;EAClE;AACF,CAAC;AAAC,eAEazF,UAAU;AAAA;AAAA;AAAA"}