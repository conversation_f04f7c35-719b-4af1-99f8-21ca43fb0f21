{"version": 3, "file": "IsolatedWorld.js", "sourceRoot": "", "sources": ["../../../../src/cdp/IsolatedWorld.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMH,OAAO,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAC;AAGtC,OAAO,EAAC,UAAU,EAAE,4BAA4B,EAAC,MAAM,mBAAmB,CAAC;AAC3E,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAC,aAAa,EAAC,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAC,KAAK,EAAC,MAAM,kBAAkB,CAAC;AAGvC,OAAO,EAAC,gBAAgB,EAAE,eAAe,EAAC,MAAM,uBAAuB,CAAC;AAGxE,OAAO,EAAC,cAAc,EAAC,MAAM,YAAY,CAAC;AAoB1C;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,KAAK;IACtC,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAoB,CAAC;IAE/C,oEAAoE;IACpE,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;IAErC,+EAA+E;IAC/E,SAAS,GAAG,IAAI,GAAG,EAAmB,CAAC;IAEvC,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,cAAc,CAA0B;IAEjD,YACE,aAAsC,EACtC,eAAgC;QAEhC,KAAK,CAAC,eAAe,CAAC,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,YAAY;QACV,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACjE,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;IACpC,CAAC;IAED,YAAY;QACV,2EAA2E;QAC3E,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;QAClC,IAAI,qBAAqB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACjD,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,UAAU,CAAC,OAAyB;QAClC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC/B,KAAK,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IACnC,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAED,iBAAiB;QACf,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CACb,yDAAyD,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,iCAAiC,CACjH,CAAC;QACJ,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,4BAA4B,CACzC,IAAI,CAAC,cAAc,CAAC,IAAI,EACxB,YAAY,CACb,CAAC;QACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/C,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,4BAA4B,CACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAClB,YAAY,CACb,CAAC;QACF,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,YAAY,gBAAgB,CAAC,EAAE,CAAC;YACvD,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,CAAC;QACD,OAAO,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,yEAAyE;IACzE,yEAAyE;IACzE,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;IACrB,KAAK,CAAC,oBAAoB,CACxB,OAAyB,EACzB,IAAY;;;YAEZ,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpC,OAAO;YACT,CAAC;YAED,MAAM,CAAC,kCAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,QAAA,CAAC;YACtC,IAAI,CAAC;gBACH,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CACxB,oBAAoB,EACpB,OAAO,CAAC,YAAY;oBAClB,CAAC,CAAC;wBACE,IAAI;wBACJ,oBAAoB,EAAE,OAAO,CAAC,YAAY;qBAC3C;oBACH,CAAC,CAAC;wBACE,IAAI;wBACJ,kBAAkB,EAAE,OAAO,CAAC,UAAU;qBACvC,CACN,CAAC;gBAEF,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;gBAEzD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,iEAAiE;gBACjE,uEAAuE;gBACvE,mCAAmC;gBACnC,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;oBAC3B,qBAAqB;oBACrB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAC,EAAE,CAAC;wBAC9D,OAAO;oBACT,CAAC;oBACD,mBAAmB;oBACnB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC,EAAE,CAAC;wBACpE,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,UAAU,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;;;;;;;;;KACF;IAED,gBAAgB,GAAG,KAAK,EACtB,KAA0C,EAC3B,EAAE;QACjB,IAAI,OAAuB,CAAC;QAC5B,IAAI,CAAC;YACH,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAAC,MAAM,CAAC;YACP,mEAAmE;YACnE,6CAA6C;YAC7C,OAAO;QACT,CAAC;QACD,MAAM,EAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAC,GAAG,OAAO,CAAC;QACnD,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YACnD,IAAI,KAAK,CAAC,kBAAkB,KAAK,OAAO,CAAC,UAAU,EAAE,CAAC;gBACpD,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,UAAU,CAAC,GAAG,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC7B,aAA0C;QAE1C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACxD,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzD,aAAa,EAAE,aAAa;YAC5B,kBAAkB,EAAE,gBAAgB,CAAC,UAAU;SAChD,CAAC,CAAC;QACH,OAAO,eAAe,CAAC,IAAI,EAAE,MAAM,CAAmB,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,WAAW,CAA2B,MAAS;QACnD,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1B,yEAAyE;YACzE,oCAAoC;YACpC,OAAO,CAAC,MAAM,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBAC1C,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAiB,CAAC;QACtB,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1D,QAAQ,EAAE,MAAM,CAAC,EAAE;SACpB,CAAC,CAAC;QACH,OAAO,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAM,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,cAAc,CAA2B,MAAS;QACtD,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1B,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,4CAA4C;QAC5C,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACjD,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACtD,QAAQ,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,QAAQ;SACzC,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAC5C,IAAI,CAAC,IAAI,CAAC,aAAa,CACxB,CAAM,CAAC;QACR,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,CAAC,aAAa,CAAC;QACb,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAClE,CAAC;CACF"}