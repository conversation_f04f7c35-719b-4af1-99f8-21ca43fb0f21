{"version": 3, "file": "HTTPRequest.d.ts", "sourceRoot": "", "sources": ["../../../../src/cdp/HTTPRequest.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AACrD,OAAO,KAAK,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAC;AAC3C,OAAO,EACL,KAAK,wBAAwB,EAC7B,KAAK,SAAS,EAEd,WAAW,EAEX,KAAK,wBAAwB,EAC7B,KAAK,YAAY,EACjB,KAAK,kBAAkB,EAExB,MAAM,uBAAuB,CAAC;AAK/B,OAAO,KAAK,EAAC,eAAe,EAAC,MAAM,mBAAmB,CAAC;AAEvD;;GAEG;AACH,qBAAa,cAAe,SAAQ,WAAW;;IACrC,cAAc,EAAE,cAAc,EAAE,CAAC;IACjC,SAAS,EAAE,eAAe,GAAG,IAAI,CAAC;IAuB1C,IAAa,MAAM,IAAI,UAAU,CAEhC;gBAGC,MAAM,EAAE,UAAU,EAClB,KAAK,EAAE,KAAK,GAAG,IAAI,EACnB,cAAc,EAAE,MAAM,GAAG,SAAS,EAClC,iBAAiB,EAAE,OAAO,EAC1B,IAAI,EAAE;QACJ;;WAEG;QACH,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;QACtC;;WAEG;QACH,QAAQ,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC;QACrC;;WAEG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB;;WAEG;QACH,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC;QAClC;;WAEG;QACH,SAAS,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;QACvC;;WAEG;QACH,IAAI,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC;KACtC,EACD,aAAa,EAAE,cAAc,EAAE;IAyBxB,GAAG,IAAI,MAAM;IAIb,wBAAwB,IAAI,wBAAwB;IAKpD,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI;IAKxD,gBAAgB,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI;IAKvD,wBAAwB,IAAI,wBAAwB;IAUpD,4BAA4B,IAAI,OAAO;IAIhD,sBAAsB,CACpB,cAAc,EAAE,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,GAChD,IAAI;IAIQ,qBAAqB,IAAI,OAAO,CAAC,IAAI,CAAC;IAkB5C,YAAY,IAAI,YAAY;IAI5B,MAAM,IAAI,MAAM;IAIhB,QAAQ,IAAI,MAAM,GAAG,SAAS;IAI9B,WAAW,IAAI,OAAO;IAIhB,aAAa,IAAI,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;IAYlD,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAIjC,QAAQ,IAAI,eAAe,GAAG,IAAI;IAIlC,KAAK,IAAI,KAAK,GAAG,IAAI;IAIrB,mBAAmB,IAAI,OAAO;IAI9B,SAAS,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS;IAInD,aAAa,IAAI,cAAc,EAAE;IAIjC,OAAO,IAAI;QAAC,SAAS,EAAE,MAAM,CAAA;KAAC,GAAG,IAAI;IAS/B,QAAQ,CACrB,SAAS,GAAE,wBAA6B,EACxC,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;IA6DD,OAAO,CACpB,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,EACrC,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;IA8ED,KAAK,CAClB,SAAS,GAAE,SAAoB,EAC/B,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;CAyCjB"}