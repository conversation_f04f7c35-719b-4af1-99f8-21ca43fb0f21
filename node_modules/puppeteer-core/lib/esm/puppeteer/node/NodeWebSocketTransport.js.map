{"version": 3, "file": "NodeWebSocketTransport.js", "sourceRoot": "", "sources": ["../../../../src/node/NodeWebSocketTransport.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,aAAa,MAAM,IAAI,CAAC;AAG/B,OAAO,EAAC,cAAc,EAAC,MAAM,yBAAyB,CAAC;AAEvD;;GAEG;AACH,MAAM,OAAO,sBAAsB;IACjC,MAAM,CAAC,MAAM,CACX,GAAW,EACX,OAAgC;QAEhC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,EAAE,GAAG,IAAI,aAAa,CAAC,GAAG,EAAE,EAAE,EAAE;gBACpC,eAAe,EAAE,IAAI;gBACrB,iBAAiB,EAAE,KAAK;gBACxB,UAAU,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ;gBACvC,OAAO,EAAE;oBACP,YAAY,EAAE,aAAa,cAAc,EAAE;oBAC3C,GAAG,OAAO;iBACX;aACF,CAAC,CAAC;YAEH,EAAE,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;gBAC/B,OAAO,OAAO,CAAC,IAAI,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,GAAG,CAAgB;IACnB,SAAS,CAAyC;IAClD,OAAO,CAAc;IAErB,YAAY,EAAiB;QAC3B,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE;YAC3C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACtC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;QACH,mEAAmE;QACnE,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI,CAAC,OAAe;QAClB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAED,KAAK;QACH,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;IACnB,CAAC;CACF"}