{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Browser.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAC,eAAe,EAAE,eAAe,EAAC,MAAM,0BAA0B,CAAC;AAC1E,OAAO,EAAC,eAAe,EAAE,aAAa,EAAC,MAAM,0BAA0B,CAAC;AAKxE,OAAO,EAAC,WAAW,EAAC,MAAM,kBAAkB,CAAC;AAY7C;;GAEG;IACU,OAAO;sBAAS,YAAY;;;;;;;iBAA5B,OAAQ,SAAQ,WAgB3B;;;YA8GA,wKAAA,OAAO,6DAIN;YAMD,kKAAM,KAAK,6DAMV;YAMD,mMAAM,gBAAgB,6DAcrB;YAMD,4MAAM,mBAAmB,6DAIxB;YAMD,sMAAM,iBAAiB,6DAkBtB;;;QAnLD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAgB;YAChC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5B,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,oBAAoB;QACpB,OAAO,yDAAG,KAAK,EAAC;QAChB,OAAO,CAAqB;QACnB,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;QACrC,aAAa,GAAG,IAAI,GAAG,EAAuB,CAAC;QAC/C,OAAO,CAAU;QAC1B,kBAAkB;QAElB,YAAoB,OAAgB;YAClC,KAAK,EAAE,CAAC;YACR,oBAAoB;YACpB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,kBAAkB;YAElB,IAAI,CAAC,aAAa,CAAC,GAAG,CACpB,WAAW,CAAC,OAAO,EACnB,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,CAC9C,CAAC;QACJ,CAAC;QAED,KAAK,CAAC,WAAW;YACf,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC1C,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAC/B,CAAC;YACF,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE;gBACxC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;YAEH,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;gBAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;oBAClC,oCAAoC;gBACtC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrC,CAAC;QAED,KAAK,CAAC,iBAAiB;YACrB,MAAM,EACJ,MAAM,EAAE,EAAC,YAAY,EAAC,GACvB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;YAE3D,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;gBACnC,IAAI,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,OAAO,EAAE,CAAC;oBAChD,SAAS;gBACX,CAAC;gBACD,IAAI,CAAC,aAAa,CAAC,GAAG,CACpB,OAAO,CAAC,WAAW,EACnB,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,CAC9C,CAAC;YACJ,CAAC;QACH,CAAC;QAED,KAAK,CAAC,qBAAqB;YACzB,0EAA0E;YAC1E,sBAAsB;YACtB,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAC;YACrC,IAAI,QAAqC,CAAC;YAE1C,CAAC;;;oBACC,MAAM,cAAc,kCAAG,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,QAAA,CAAC;oBACtD,cAAc,CAAC,EAAE,CAAC,gCAAgC,EAAE,IAAI,CAAC,EAAE;wBACzD,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC/B,CAAC,CAAC,CAAC;oBACH,cAAc,CAAC,EAAE,CAAC,kCAAkC,EAAE,IAAI,CAAC,EAAE;wBAC3D,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAClC,CAAC,CAAC,CAAC;oBACH,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;oBACxE,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;;;;;;;;;aAC5B;YAED,uDAAuD;YACvD,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;gBAC5B,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBACjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE,IAAI,CAAC,CAAC;gBAC5D,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,IAAI,MAAM;YACR,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QACD,IAAI,kBAAkB;YACpB,mEAAmE;YACnE,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAE,CAAC;QACtD,CAAC;QACD,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;QACpC,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC;QACD,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QACrC,CAAC;QACD,kBAAkB;QAGlB,OAAO,CAAC,MAAe,EAAE,MAAM,GAAG,KAAK;YACrC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAMD,KAAK,CAAC,KAAK;YACT,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;YAC/C,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAMD,KAAK,CAAC,gBAAgB,CACpB,mBAA2B,EAC3B,UAAmC,EAAE;YAErC,MAAM,EACJ,MAAM,EAAE,EAAC,MAAM,EAAC,GACjB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACrD,mBAAmB;gBACnB,GAAG,OAAO;gBACV,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE;oBACxC,OAAO,OAAO,CAAC,EAAE,CAAC;gBACpB,CAAC,CAA0B;aAC5B,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;QAMD,KAAK,CAAC,mBAAmB,CAAC,MAAc;YACtC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACpD,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAMD,KAAK,CAAC,iBAAiB;YACrB,MAAM,EACJ,MAAM,EAAE,EAAC,WAAW,EAAE,OAAO,EAAC,GAC/B,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;YAE7D,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACtD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;YAEpD,MAAM,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC9C,IAAI,YAAY,CAAC,WAAW,CAAC,CAC9B,CAAC;YACF,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACrC,kBAAkB,CAAC,kBAAkB,EAAE,CAAC;gBAExC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,yBAzEC,eAAe,wBAOf,eAAe,CAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,mCASD,eAAe,CAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,sCAiBD,eAAe,CAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,oCAOD,eAAe,CAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,GAqBD,aAAa,EAAC;YACb,IAAI,CAAC,OAAO;gBACV,+DAA+D,CAAC;YAClE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAC9C,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAElD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;QACzB,CAAC;;;SAhNU,OAAO"}