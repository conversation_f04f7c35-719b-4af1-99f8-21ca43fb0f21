{"version": 3, "file": "Frame.d.ts", "sourceRoot": "", "sources": ["../../../../src/bidi/Frame.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAcH,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AACrD,OAAO,KAAK,EAAC,aAAa,EAAC,MAAM,yBAAyB,CAAC;AAC3D,OAAO,EACL,KAAK,EAEL,KAAK,WAAW,EAChB,KAAK,cAAc,EACpB,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,EAAC,sBAAsB,EAAC,MAAM,gBAAgB,CAAC;AAE3D,OAAO,KAAK,EAAC,eAAe,EAAC,MAAM,8BAA8B,CAAC;AAClE,OAAO,KAAK,EAAC,SAAS,EAAE,OAAO,EAAC,MAAM,oBAAoB,CAAC;AAQ3D,OAAO,EAAC,aAAa,EAAC,MAAM,uBAAuB,CAAC;AAEpD,OAAO,KAAK,EAAC,eAAe,EAAC,MAAM,sBAAsB,CAAC;AAE1D,OAAO,KAAK,EAAC,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AAMxD,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,WAAW,CAAC;AACxC,OAAO,EAGL,OAAO,EACP,KAAK,YAAY,EAClB,MAAM,cAAc,CAAC;AAEtB;;;GAGG;AACH,qBAAa,SAAU,SAAQ,KAAK;;IAMlC,SAAS,EAAE,YAAY,CAAC;IACf,GAAG,EAAE,MAAM,CAAC;gBAGnB,IAAI,EAAE,QAAQ,EACd,OAAO,EAAE,eAAe,EACxB,eAAe,EAAE,eAAe,EAChC,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI;IAoB1B,IAAa,MAAM,IAAI,UAAU,CAEhC;IAEQ,SAAS,IAAI,OAAO;IAIpB,aAAa,IAAI,OAAO;IAIxB,IAAI,IAAI,QAAQ;IAIhB,UAAU,IAAI,KAAK;IAInB,GAAG,IAAI,MAAM;IAIb,WAAW,IAAI,SAAS,GAAG,IAAI;IAI/B,WAAW,IAAI,SAAS,EAAE;IAKpB,IAAI,CACjB,GAAG,EAAE,MAAM,EACX,OAAO,GAAE,WAAgB,GACxB,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAsCpB,UAAU,CACvB,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,IAAI,CAAC;IAkChB,OAAO,IAAI,eAAe;IAKX,iBAAiB,CAC9B,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAmD1B,mBAAmB,IAAI,KAAK;IAIrC,IAAa,QAAQ,IAAI,OAAO,CAE/B;IAED,CAAC,aAAa,CAAC,IAAI,IAAI;IAYjB,cAAc,CAAC,IAAI,SAAS,OAAO,EAAE,EAAE,GAAG,EAC9C,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,KAAK,SAAS,CAAC,GAAG,CAAC,GACvC,OAAO,CAAC,IAAI,CAAC;IAgBP,eAAe,CAAC,QAAQ,SAAS,MAAM,EAC9C,QAAQ,EAAE,QAAQ,EAClB,OAAO,CAAC,EAAE,sBAAsB,GAC/B,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;CASpD"}