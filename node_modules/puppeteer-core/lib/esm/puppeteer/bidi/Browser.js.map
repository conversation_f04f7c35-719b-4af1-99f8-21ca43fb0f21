{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../src/bidi/Browser.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAMH,OAAO,EACL,OAAO,GAKR,MAAM,mBAAmB,CAAC;AAI3B,OAAO,EAAC,oBAAoB,EAAC,MAAM,qBAAqB,CAAC;AAEzD,OAAO,EAAC,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAG7C,OAAO,EAAC,kBAAkB,EAAC,MAAM,qBAAqB,CAAC;AACvD,OAAO,EAAC,eAAe,EAAE,oBAAoB,EAAC,MAAM,sBAAsB,CAAC;AAG3E,OAAO,EAAC,OAAO,EAAC,MAAM,mBAAmB,CAAC;AAE1C,OAAO,EACL,iBAAiB,EACjB,yBAAyB,EACzB,cAAc,GAEf,MAAM,aAAa,CAAC;AAarB;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,OAAO;IAC7B,QAAQ,GAAG,eAAe,CAAC;IAEpC,iDAAiD;IACjD,MAAM,CAAU,gBAAgB,GAAa;QAC3C,iBAAiB;QACjB,SAAS;QACT,KAAK;QACL,QAAQ;KACT,CAAC;IACF,MAAM,CAAU,kBAAkB,GAA0B;QAC1D,WAAW;QACX,2BAA2B;QAC3B,yBAAyB;QACzB,sCAAsC;QACtC,UAAU;QACV,6BAA6B;QAC7B,mDAAmD;QACnD,+BAA+B;QAC/B,2BAA2B;QAC3B,0BAA0B;KAC3B,CAAC;IAEF,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAwB;QAC1C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClD,WAAW,EAAE;gBACX,mBAAmB,EAAE,IAAI,CAAC,iBAAiB;gBAC3C,YAAY,EAAE,IAAI;aACnB;SACF,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,SAAS,CACrB,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;YACtE,CAAC,CAAC,WAAW,CAAC,gBAAgB;YAC9B,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,gBAAgB,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CACzE,CAAC;QAEF,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACvD,OAAO,CAAC,WAAW,EAAE,CAAC;QACtB,MAAM,OAAO,CAAC,QAAQ,EAAE,CAAC;QACzB,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAgB;IACxB,cAAc,CAAwB;IACtC,YAAY,CAAc;IAC1B,gBAAgB,CAAkB;IAClC,QAAQ,GAAG,IAAI,GAAG,EAAsB,CAAC;IACzC,gBAAgB,GAAG,IAAI,OAAO,EAAmC,CAAC;IAClE,cAAc,CAAoB;IAElC,wBAAwB,GAAG,IAAI,GAAG,CAGhC;QACA,CAAC,gCAAgC,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrE,CAAC,kCAAkC,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzE,CAAC,kCAAkC,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzE,CAAC,mCAAmC,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3E,CAAC,mCAAmC,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC5E,CAAC,CAAC;IAEH,YAAoB,WAAwB,EAAE,IAAwB;QACpE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QAC7C,IAAI,CAAC,cAAc,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAClD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACrD,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,WAAW;QACT,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE;YAC1C,IAAI,CAAC,IAAI,iDAA4B,SAAS,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;YAChC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;YAC3D,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACjE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC;IAC5D,CAAC;IACD,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC;IAC/D,CAAC;IAEQ,SAAS;QAChB,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;IAED,qBAAqB,CAAC,WAAwB;QAC5C,MAAM,cAAc,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,WAAW,EAAE;YAC/D,eAAe,EAAE,IAAI,CAAC,gBAAgB;SACvC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QACvD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,mBAAmB,CAAC,KAAgC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,mDAA6B,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,0DAAoC,MAAM,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,oBAAoB,CAAC,KAA0C;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,mDAA6B,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,0DAAoC,MAAM,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,iBAAiB,CAAC,KAAoD;QACpE,MAAM,OAAO,GAAG,IAAI,eAAe,CACjC,IAAI,CAAC,UAAU,EACf,KAAK,EACL,IAAI,CAAC,YAAY,CAClB,CAAC;QACF,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,cAAc,GAClB,KAAK,CAAC,WAAW,KAAK,SAAS;YAC7B,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC9B,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;gBAC3C,OAAO,cAAc,CAAC,EAAE,KAAK,KAAK,CAAC,WAAW,CAAC;YACjD,CAAC,CAAC,CAAC;QACT,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QACD,MAAM,MAAM,GAAG,CAAC,OAAO,CAAC,MAAM;YAC5B,CAAC,CAAC,IAAI,cAAc,CAAC,cAAc,EAAE,OAAO,CAAC;YAC7C,CAAC,CAAC,IAAI,yBAAyB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAEzC,IAAI,CAAC,IAAI,mDAA6B,MAAM,CAAC,CAAC;QAC9C,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,0DAAoC,MAAM,CAAC,CAAC;QAExE,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACpE,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;QAC3E,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACtC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,KAAsD;QAEtD,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClE,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1E,eAAe,CAAC,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAChD,MAAM,IAAI,GAAG,MAAM,MAAM,EAAE,IAAI,EAAE,CAAC;QAClC,MAAM,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACtC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACpC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,uDAA+B,MAAM,CAAC,CAAC;YAChD,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,8DAAsC,MAAM,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED,IAAI,UAAU;QACZ,2CAA2C;QAC3C,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAA4B,CAAC;IAChE,CAAC;IAEQ,UAAU;QACjB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;IAC7B,CAAC;IAEQ,KAAK,CAAC,KAAK;QAClB,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACjE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,iBAAiB;YACjB,UAAU,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,IAAa,SAAS;QACpB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;IACrC,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;IAC/B,CAAC;IAEQ,KAAK,CAAC,6BAA6B,CAC1C,QAAgC;QAEhC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;QAChE,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;IACjD,CAAC;IAEQ,KAAK,CAAC,OAAO;QACpB,OAAO,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;IACxD,CAAC;IAEQ,eAAe;QACtB,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACvD,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,qBAAqB;QAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAE,CAAC;IAC1E,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,OAAO,EAAE,CAAC;IAChD,CAAC;IAEQ,OAAO;QACd,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,cAAc,CAAC,EAAU;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;QACtC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEQ,MAAM;QACb,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEQ,KAAK,CAAC,UAAU;QACvB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,iBAAiB;YACjB,UAAU,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,IAAa,SAAS;QACpB,OAAO;YACL,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE;SAClE,CAAC;IACJ,CAAC"}