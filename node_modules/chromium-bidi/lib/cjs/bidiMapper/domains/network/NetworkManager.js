"use strict";
/*
 * Copyright 2023 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkManager = void 0;
const NetworkRequest_js_1 = require("./NetworkRequest.js");
/** Maps 1:1 to CdpTarget. */
class NetworkManager {
    #cdpTarget;
    #eventManager;
    #networkStorage;
    constructor(cdpTarget, eventManager, networkStorage) {
        this.#cdpTarget = cdpTarget;
        this.#eventManager = eventManager;
        this.#networkStorage = networkStorage;
    }
    /** Returns the CDP Target associated with this NetworkManager instance. */
    get cdpTarget() {
        return this.#cdpTarget;
    }
    /**
     * Gets the network request with the given ID, if any.
     * Otherwise, creates a new network request with the given ID and cdp target.
     */
    #getOrCreateNetworkRequest(id, redirectCount) {
        let request = this.#networkStorage.getRequest(id);
        if (request) {
            return request;
        }
        request = new NetworkRequest_js_1.NetworkRequest(id, this.#eventManager, this.#networkStorage, this.#cdpTarget, redirectCount);
        this.#networkStorage.addRequest(request);
        return request;
    }
    static create(cdpTarget, eventManager, networkStorage) {
        const networkManager = new NetworkManager(cdpTarget, eventManager, networkStorage);
        cdpTarget.browserCdpClient.on('Target.detachedFromTarget', (params) => {
            if (cdpTarget.cdpClient.sessionId === params.sessionId) {
                networkManager.#networkStorage.disposeRequestMap();
            }
        });
        cdpTarget.cdpClient.on('Network.requestWillBeSent', (params) => {
            const request = networkManager.#networkStorage.getRequest(params.requestId);
            if (request && request.isRedirecting()) {
                request.handleRedirect(params);
                networkManager.#networkStorage.deleteRequest(params.requestId);
                networkManager
                    .#getOrCreateNetworkRequest(params.requestId, request.redirectCount + 1)
                    .onRequestWillBeSentEvent(params);
            }
            else if (request) {
                request.onRequestWillBeSentEvent(params);
            }
            else {
                networkManager
                    .#getOrCreateNetworkRequest(params.requestId)
                    .onRequestWillBeSentEvent(params);
            }
        });
        cdpTarget.cdpClient.on('Network.requestWillBeSentExtraInfo', (params) => {
            networkManager
                .#getOrCreateNetworkRequest(params.requestId)
                .onRequestWillBeSentExtraInfoEvent(params);
        });
        cdpTarget.cdpClient.on('Network.responseReceived', (params) => {
            networkManager
                .#getOrCreateNetworkRequest(params.requestId)
                .onResponseReceivedEvent(params);
        });
        cdpTarget.cdpClient.on('Network.responseReceivedExtraInfo', (params) => {
            networkManager
                .#getOrCreateNetworkRequest(params.requestId)
                .onResponseReceivedExtraInfoEvent(params);
        });
        cdpTarget.cdpClient.on('Network.requestServedFromCache', (params) => {
            networkManager
                .#getOrCreateNetworkRequest(params.requestId)
                .onServedFromCache();
        });
        cdpTarget.cdpClient.on('Network.loadingFailed', (params) => {
            networkManager
                .#getOrCreateNetworkRequest(params.requestId)
                .onLoadingFailedEvent(params);
        });
        // https://chromedevtools.github.io/devtools-protocol/tot/Fetch/#event-requestPaused
        cdpTarget.cdpClient.on('Fetch.requestPaused', (params) => {
            if (params.networkId) {
                networkManager
                    .#getOrCreateNetworkRequest(params.networkId)
                    .onRequestPaused(params);
            }
        });
        return networkManager;
    }
}
exports.NetworkManager = NetworkManager;
//# sourceMappingURL=NetworkManager.js.map