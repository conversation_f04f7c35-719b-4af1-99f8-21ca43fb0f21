import type { Protocol } from 'devtools-protocol';
import type { CdpClient } from '../../../cdp/CdpClient.js';
import { Deferred } from '../../../utils/Deferred.js';
import type { LoggerFn } from '../../../utils/log.js';
import type { Result } from '../../../utils/result.js';
import type { NetworkStorage } from '../network/NetworkStorage.js';
import type { ChannelProxy } from '../script/ChannelProxy.js';
import type { PreloadScriptStorage } from '../script/PreloadScriptStorage.js';
import type { RealmStorage } from '../script/RealmStorage.js';
import type { EventManager } from '../session/EventManager.js';
export declare class CdpTarget {
    #private;
    static create(targetId: Protocol.Target.TargetID, cdpClient: CdpClient, browserCdpClient: CdpClient, realmStorage: RealmStorage, eventManager: EventManager, preloadScriptStorage: PreloadScriptStorage, networkStorage: NetworkStorage, acceptInsecureCerts: boolean, logger?: LoggerFn): CdpTarget;
    constructor(targetId: Protocol.Target.TargetID, cdpClient: CdpClient, browserCdpClient: CdpClient, eventManager: EventManager, preloadScriptStorage: PreloadScriptStorage, networkStorage: NetworkStorage, acceptInsecureCerts: boolean);
    /** Returns a deferred that resolves when the target is unblocked. */
    get unblocked(): Deferred<Result<void>>;
    get id(): Protocol.Target.TargetID;
    get cdpClient(): CdpClient;
    get browserCdpClient(): CdpClient;
    /** Needed for CDP escape path. */
    get cdpSessionId(): Protocol.Target.SessionID;
    /** Calls `Fetch.enable` with the added network intercepts. */
    fetchEnable(): Promise<void>;
    /** Calls `Fetch.disable`. */
    fetchDisable(): Promise<void>;
    /**
     * All the ProxyChannels from all the preload scripts of the given
     * BrowsingContext.
     */
    getChannels(): ChannelProxy[];
}
