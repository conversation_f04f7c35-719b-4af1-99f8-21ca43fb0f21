{"version": 3, "file": "PDFAnnotation.js", "sourceRoot": "", "sources": ["../../../src/core/annotation/PDFAnnotation.ts"], "names": [], "mappings": "AAAA,OAAO,OAAO,2BAAiC;AAC/C,OAAO,OAAO,2BAAiC;AAC/C,OAAO,SAAS,6BAAmC;AACnD,OAAO,QAAQ,4BAAkC;AACjD,OAAO,MAAM,0BAAgC;AAC7C,OAAO,SAAS,6BAAmC;AAEnD;IAKE,uBAAsB,IAAa;QACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,+CAA+C;IAC/C,4BAAI,GAAJ;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;IACxD,CAAC;IAED,0BAAE,GAAF;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED,yBAAC,GAAD;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACtD,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAC/D,CAAC;IAED,oCAAY,GAAZ;;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACzB,aAAO,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,qCAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;IACpE,CAAC;IAED,oCAAY,GAAZ,UAAa,IAA6D;QAChE,IAAA,CAAC,GAAuB,IAAI,EAA3B,EAAE,CAAC,GAAoB,IAAI,EAAxB,EAAE,KAAK,GAAa,IAAI,MAAjB,EAAE,MAAM,GAAK,IAAI,OAAT,CAAU;QACrC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,0CAAkB,GAAlB;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9C,IAAI,EAAE,YAAY,OAAO;YAAE,OAAO,EAAE,CAAC;QACrC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,0CAAkB,GAAlB,UAAmB,KAAc;QAC/B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC;IAED,sCAAc,GAAd,UAAe,WAAoB;QACjC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC;IAC/C,CAAC;IAED,gCAAQ,GAAR;QACE,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACnB,IAAI,CAAC,EAAE,EAAE;YACP,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;SACrC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,2CAAmB,GAAnB;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,IAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,YAAY,MAAM,IAAI,CAAC,YAAY,OAAO;YAAE,OAAO,CAAC,CAAC;QAE1D,MAAM,IAAI,KAAK,CAAC,yBAAsB,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,WAAW,CAAC,IAAI,CAAE,CAAC,CAAC;IAC/D,CAAC;IAED,+DAA+D;IAC/D,2CAAmB,GAAnB,UAAoB,UAA4B;QAC9C,IAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED,+DAA+D;IAC/D,6CAAqB,GAArB,UAAsB,UAA4B;QAChD,IAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED,+DAA+D;IAC/D,yCAAiB,GAAjB,UAAkB,UAA4B;QAC5C,IAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED,gDAAwB,GAAxB;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACrB,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;IAC9B,CAAC;IAED,4CAAoB,GAApB;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACrB,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;IAC9B,CAAC;IAED,sCAAc,GAAd;QAOE,IAAM,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QAErB,IAAI,CAAC,EAAE;YAAE,OAAO,SAAS,CAAC;QAE1B,IAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QACzD,IAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QAC9D,IAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QAE9D,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;IAC7C,CAAC;IAED,gCAAQ,GAAR;;QACE,mBAAO,IAAI,CAAC,CAAC,EAAE,0CAAE,QAAQ,qCAAM,CAAC,CAAC;IACnC,CAAC;IAED,gCAAQ,GAAR,UAAS,KAAa;QACpB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACtD,CAAC;IAED,+BAAO,GAAP,UAAQ,IAAY;QAClB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,+BAAO,GAAP,UAAQ,IAAY;QAClB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,iCAAS,GAAT,UAAU,IAAY;QACpB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,iCAAS,GAAT,UAAU,IAAY,EAAE,MAAe;QACrC,IAAI,MAAM;YAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;YAC1B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAtIM,sBAAQ,GAAG,UAAC,IAAa,IAAoB,OAAA,IAAI,aAAa,CAAC,IAAI,CAAC,EAAvB,CAAuB,CAAC;IAuI9E,oBAAC;CAAA,AA1ID,IA0IC;AAED,eAAe,aAAa,CAAC"}