<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحفة العالمية النهائية - غلاف كتاب تعليم الإنجليزية</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700;900&family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .book-cover {
            width: 470px;
            height: 705px;
            position: relative;
            background: linear-gradient(135deg, #0f172a 0%, #1e40af 30%, #3b82f6 70%, #60a5fa 100%);
            overflow: hidden;
            font-family: 'Cairo', '<PERSON><PERSON>', sans-serif;
            box-shadow: 0 25px 50px rgba(0,0,0,0.4);
            border: 2px solid rgba(255,255,255,0.1);
        }

        /* خلفية الخرائط الذهنية */
        .mind-map-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image: 
                radial-gradient(circle at 20% 30%, rgba(255,255,255,0.3) 2px, transparent 2px),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.3) 2px, transparent 2px),
                radial-gradient(circle at 60% 70%, rgba(255,255,255,0.3) 2px, transparent 2px),
                radial-gradient(circle at 30% 80%, rgba(255,255,255,0.3) 2px, transparent 2px);
            background-size: 50px 50px;
        }

        /* خطوط الاتصال للخرائط الذهنية */
        .mind-map-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.15;
        }

        .mind-map-lines::before,
        .mind-map-lines::after {
            content: '';
            position: absolute;
            background: rgba(255,255,255,0.3);
        }

        .mind-map-lines::before {
            top: 30%;
            left: 20%;
            width: 60%;
            height: 2px;
            transform: rotate(-15deg);
        }

        .mind-map-lines::after {
            top: 60%;
            left: 15%;
            width: 70%;
            height: 2px;
            transform: rotate(25deg);
        }

        /* منطقة العنوان الرئيسي */
        .title-section {
            position: absolute;
            top: 80px;
            left: 0;
            right: 0;
            text-align: center;
            padding: 0 30px;
            z-index: 10;
        }

        .main-title {
            font-family: 'Cairo', sans-serif;
            font-size: 34px;
            font-weight: 900;
            color: #ffffff;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.7), 0 0 20px rgba(255,255,255,0.3);
            line-height: 1.1;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #ffffff, #fbbf24, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: 1px;
        }

        .subtitle {
            font-family: 'Cairo', sans-serif;
            font-size: 18px;
            font-weight: 600;
            color: #e0f2fe;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            margin-bottom: 20px;
        }

        /* شارة الخرائط الذهنية */
        .mind-map-badge {
            display: inline-block;
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            margin-bottom: 30px;
        }

        /* منطقة الوسط - العناصر البصرية */
        .visual-section {
            position: absolute;
            top: 280px;
            left: 0;
            right: 0;
            height: 200px;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 5;
        }

        /* دائرة الخريطة الذهنية المركزية */
        .central-mind-map {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4);
            position: relative;
        }

        .central-mind-map::before {
            content: '🧠';
            font-size: 48px;
            filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
        }

        /* العقد الفرعية */
        .mind-node {
            position: absolute;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .mind-node:nth-child(1) { top: -80px; left: -30px; }
        .mind-node:nth-child(2) { top: -80px; right: -30px; }
        .mind-node:nth-child(3) { bottom: -80px; left: -30px; }
        .mind-node:nth-child(4) { bottom: -80px; right: -30px; }

        /* منطقة المؤلف */
        .author-section {
            position: absolute;
            bottom: 120px;
            left: 0;
            right: 0;
            text-align: center;
            z-index: 10;
        }

        .author-name {
            font-family: 'Cairo', sans-serif;
            font-size: 20px;
            font-weight: 700;
            color: #ffffff;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
            margin-bottom: 10px;
        }

        .author-title {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            font-weight: 400;
            color: #e0f2fe;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        /* شارة الجودة */
        .quality-badge {
            position: absolute;
            top: 30px;
            right: 30px;
            background: linear-gradient(45deg, #dc2626, #ef4444);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
            z-index: 15;
        }

        /* تأثيرات الإضاءة */
        .light-effect {
            position: absolute;
            top: -50px;
            left: -50px;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
            z-index: 2;
        }

        /* خط الفصل الذهبي */
        .golden-divider {
            position: absolute;
            bottom: 200px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #fbbf24, transparent);
            z-index: 8;
        }

        /* تحسينات للطباعة عالية الجودة */
        @media print {
            .book-cover {
                width: 1410px;
                height: 2115px;
            }
            
            .main-title {
                font-size: 96px;
            }
            
            .subtitle {
                font-size: 54px;
            }
            
            .author-name {
                font-size: 60px;
            }
        }

        /* تأثيرات الحركة للمعاينة */
        .mind-node {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .central-mind-map {
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4); }
            to { box-shadow: 0 8px 35px rgba(251, 191, 36, 0.6); }
        }
    </style>
</head>
<body>
    <div class="book-cover" id="ultimate_global_masterpiece">
        <!-- خلفية الخرائط الذهنية -->
        <div class="mind-map-bg"></div>
        <div class="mind-map-lines"></div>
        
        <!-- تأثير الإضاءة -->
        <div class="light-effect"></div>
        
        <!-- شارة الجودة -->
        <div class="quality-badge">
            ⭐ الأفضل
        </div>
        
        <!-- منطقة العنوان -->
        <div class="title-section">
            <h1 class="main-title">
                تعليم الإنجليزية<br>من البداية إلى الإحتراف
            </h1>
            <p class="subtitle">
                دليل شامل ومتكامل للتعلم الفعال
            </p>
            <div class="mind-map-badge">
                🧠 بأسلوب خرائط ذهنية متطورة
            </div>
        </div>
        
        <!-- المنطقة البصرية المركزية -->
        <div class="visual-section">
            <div class="central-mind-map">
                <div class="mind-node">A</div>
                <div class="mind-node">B</div>
                <div class="mind-node">C</div>
                <div class="mind-node">D</div>
            </div>
        </div>
        
        <!-- الخط الذهبي الفاصل -->
        <div class="golden-divider"></div>
        
        <!-- منطقة المؤلف -->
        <div class="author-section">
            <h2 class="author-name">Teacher Zain الأستاذ زين</h2>
            <p class="author-title">خبير تعليم اللغة الإنجليزية</p>
        </div>
    </div>
</body>
</html>
